<template>
  <view class="my-page">
    <view class="header-bg"></view>
    <cu-header class="cu-header-box">
      <template #leftBtn>
        {{ '' }}
      </template>
      <template #title>{{ '' }}</template>
      <template #rightBtn>
        <view class="head-handle">
          <up-icon name="setting" size="20"></up-icon>
          <text>设置</text>
        </view>
      </template>
    </cu-header>
    <view class="main-container">
      <view class="base-info">
        <view class="head-box">
          <image src="../../static/images/head.png"></image>
        </view>
        <view class="info-box">
          <view class="phone">18231319486</view>
          <view class="transaction">交易 0 ｜ 发货 3</view>
          <view class="user-detail">
            <view class="user-detail-tag">
              未认证
              <view class="iconfont icon-owner-youjiantou"></view>
            </view>
            <view class="user-detail-tag">
              未企业认证
              <view class="iconfont icon-owner-youjiantou"></view>
            </view>
            <view class="user-detail-tag">员工</view>
          </view>
        </view>
        <view class="handle-info-btn" @tap="editPersonal">
          <view class="iconfont icon-owner-bianji"></view>
          <text>编辑资料</text>
        </view>
      </view>
      <view class="wallet-box">
        <view class="wallet">
          <view class="title-box">
            <view class="title">我的钱包</view>
            <view class="name">
              去实名
              <view class="iconfont icon-owner-youjiantou"></view>
            </view>
          </view>
          <view class="wallet-items">
            <view class="wallet-item">
              <view class="num">0</view>
              <view class="text">余额（元）</view>
            </view>
            <view class="wallet-item">
              <view class="num">98000</view>
              <view class="text">大约可借（元）</view>
            </view>
            <view class="wallet-item">
              <view class="num">0</view>
              <view class="text">优惠券</view>
            </view>
            <view class="wallet-item">
              <view class="num">0</view>
              <view class="text">积分</view>
            </view>
          </view>
        </view>
      </view>
      <!-- 退出登录区域 -->
      <view class="logout-container">
        <view class="logout-card">
          <view class="logout-btn" @click="handleLogout">
            <text class="logout-text">退出登录</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 退出登录确认弹窗 -->
  <cu-confirm-dialog ref="logoutDialog" title="确认退出" content="确定要退出登录吗？" cancel-text="再想想" confirm-text="确认登出" @confirm="confirmLogout" @cancel="cancelLogout">
  </cu-confirm-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { clearStorage } from '@/utils/storage';

const systemInfo: any = uni.getSystemInfoSync();
const heightHeader = systemInfo.safeAreaInsets.top * 2 + 80 + 'rpx';
// 这个用来拉长背景色
const heightHeaderBg = systemInfo.safeAreaInsets.top * 2 + 70 + 100 + 50 + 'rpx';
const logoutDialog = ref(null)

const editPersonal = () => {
  uni.navigateTo({
    url: '/pageCom/my/personal'
  });
};

// 退出登录
const handleLogout = () => {
  logoutDialog.value.open()
}

// 确认退出登录
const confirmLogout = () => {
  // 清除所有缓存
  clearStorage()

  // 显示退出成功提示
  uni.showToast({
    title: '退出成功',
    icon: 'success',
    duration: 1500
  })

  // 延迟跳转到登录页
  setTimeout(() => {
    uni.reLaunch({
      url: '/pages/login/login'
    })
  }, 1500)
}

// 取消退出登录
const cancelLogout = () => {
  console.log('用户取消退出登录')
}
</script>

<style lang="scss" scoped>
.header-bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: v-bind(heightHeader);
  background: linear-gradient(to right, #f7edeb, #f5eaeb, #f1cac4);
  z-index: 1;
}

:deep(.cu-header-box) {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: v-bind(heightHeader);
  background-image: linear-gradient(to right, #f7edeb, #f5eaeb, #f1cac4);
  background-size: 100% 400rpx;
  z-index: 3;

  .header-container {
    background-color: transparent;
  }

  .cu-header {
    background-color: transparent;

    .right-btn {
      .head-handle {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        text {
          font-size: 20rpx;
        }
      }
    }
  }
}

.main-container {
  width: 100%;
  position: relative;
  z-index: 1;
  padding-top: v-bind(heightHeader);

  .base-info {
    width: 100%;
    // height: 300rpx;
    background: linear-gradient(to right, #f7edeb, #f5eaeb, #f1cac4);
    padding: 40rpx 32rpx 30rpx 32rpx;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    position: relative;

    .head-box {
      width: 130rpx;
      height: 130rpx;
      border-radius: 50%;
      margin-right: 20rpx;

      image {
        width: 130rpx;
        height: 130rpx;
        border-radius: 50%;
      }
    }

    .info-box {
      height: 130rpx;
      display: flex;
      flex-direction: column;
      justify-content: space-around;

      .phone {
        font-size: $uni-font-size-lg;
        color: #333;
        font-weight: 700;
      }

      .transaction {
        font-size: $uni-font-size-sm;
        color: $uni-text-color-grey;
      }

      .user-detail {
        display: flex;

        .user-detail-tag {
          font-size: 20rpx;
          line-height: 20rpx;
          color: $uni-text-color-grey;
          border: 2rpx solid $uni-text-color-grey;
          border-radius: 4rpx;
          margin-right: 10rpx;
          padding: 4rpx 6rpx;
          box-sizing: border-box;
          display: flex;

          .iconfont {
            font-size: 20rpx;
            color: $uni-text-color-grey;
          }
        }
      }
    }

    .handle-info-btn {
      display: flex;
      align-items: center;
      background-color: #fff;
      padding: 10rpx 20rpx;
      box-sizing: border-box;
      border-top-left-radius: 30rpx;
      border-bottom-left-radius: 30rpx;
      position: absolute;
      right: 0rpx;

      .iconfont {
        font-size: $uni-font-size-base;
        color: #e44740;
        margin-right: 10rpx;
      }

      text {
        font-size: $uni-font-size-base;
        line-height: $uni-font-size-base;
        color: #333;
      }
    }
  }

  .wallet-box {
    padding: 0rpx 30rpx;
    box-sizing: border-box;
    margin-top: 20rpx;

    .wallet {
      width: 100%;
      background-color: #fff;
      border-radius: 10rpx;
      padding: 20rpx 30rpx 30rpx 30rpx;
      box-sizing: border-box;

      .title-box {
        width: 100%;
        display: flex;
        justify-content: space-between;

        .title {
          font-size: $uni-font-size-lg;
          color: #333;
          font-weight: 600;
        }

        .name {
          font-size: $uni-font-size-base;
          color: $uni-text-color-grey;
          display: flex;
          align-items: center;

          .iconfont {
            font-size: $uni-font-size-base;
            color: $uni-text-color-grey;
          }
        }
      }

      .wallet-items {
        display: flex;
        margin-top: 20rpx;

        .wallet-item {
          width: 25%;
          display: flex;
          flex-direction: column;
          align-items: center;

          .num {
            font-size: $uni-font-size-lg;
            color: #333;
          }

          .text {
            font-size: $uni-font-size-sm;
            color: $uni-text-color-grey;
          }
        }
      }
    }
  }

  .logout-container {
    padding: 0rpx 30rpx 40rpx 30rpx;
    box-sizing: border-box;
    margin-top: 300rpx;

    .logout-card {
      width: 100%;
      padding: 30rpx;
      box-sizing: border-box;
      background-color: #fff;
      border-radius: 16rpx;

      .logout-btn {
        width: 100%;
        height: 100rpx;
        background-color: #ff4757;
        border-radius: 50rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;

        &:active {
          background-color: #ff3838;
          transform: scale(0.98);
        }

        .logout-text {
          color: #fff;
          font-size: $uni-font-size-lg;
          font-weight: 600;
        }
      }
    }
  }
}
</style>
