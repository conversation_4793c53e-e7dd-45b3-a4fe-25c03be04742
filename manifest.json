{
    "name" : "运通达",
    "appid" : "__UNI__47B5CD2",
    "description" : "货运 集成货运  危化品运输 大件运输 小件运输",
    "versionName" : "1.0.4",
    "versionCode" : 104,
    "transformPx" : false,
    /* 5+App特有相关 */
    "app-plus" : {
        "optimization" : {
            "subPackages" : true
        },
        "safearea" : {
            "bottom" : {
                "offset" : "none"
            }
        },
        "runmode" : "liberate", // 开启分包优化后，必须配置资源释放模式
        "usingComponents" : true,
        "nvueStyleCompiler" : "uni-app",
        "compilerVersion" : 3,
        "splashscreen" : {
            "alwaysShowBeforeRender" : true,
            "waiting" : true,
            "autoclose" : true,
            "delay" : 0
        },
        /* 模块配置 */
        "modules" : {
            "Maps" : {},
            "Camera" : {},
            "Barcode" : {},
            "Geolocation" : {}
        },
        /* 应用发布信息 */
        "distribute" : {
            /* android打包配置 */
            "android" : {
                "permissions" : [
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>",
                    "<uses-permission android:name=\"android.permission.SEND_SMS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_LOCATION_EXTRA_COMMANDS\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_MOCK_LOCATION\"/>"
                ]
            },
            /* ios打包配置 */
            "ios" : {
                "dSYMs" : false,
                "privacyDescription" : {
                    "NSLocationAlwaysAndWhenInUseUsageDescription" : "该功能需求您同意此次请求",
                    "NSLocationAlwaysUsageDescription" : "该功能需求您同意此次请求",
                    "NSLocationWhenInUseUsageDescription" : "该功能需求您同意此次请求"
                },
                "permissions" : [ "NSClipboardUsageDescription" ]
            },
            /* SDK配置 */
            "sdkConfigs" : {
                "ad" : {},
                "geolocation" : {
                    "amap" : {
                        "name" : "amap_13512077033Cmw4xIj0U",
                        "__platform__" : [ "ios", "android" ],
                        "appkey_ios" : "3299b1cc323014e83fa1138b1211840b",
                        "appkey_android" : "535ab5092705a9387a0c373b5eeb2f49"
                    }
                },
                "maps" : {
                    "amap" : {
                        "name" : "amap_13512077033Cmw4xIj0U",
                        "appkey_ios" : "3299b1cc323014e83fa1138b1211840b",
                        "appkey_android" : "535ab5092705a9387a0c373b5eeb2f49"
                    }
                },
                "oauth" : {},
                "payment" : {
                    "weixin" : {
                        "__platform__" : [ "ios", "android" ],
                        "appid" : "您的微信开放平台AppID",
                        "UniversalLinks" : "https://您的域名/universal-links/"
                    }
                },
                "push" : {
                    "unipush" : {
                        "version" : "2",
                        "offline" : false,
                        "icons" : {
                            "small" : {
                                "hdpi" : "unpackage/res/icons/152x152.png",
                                "mdpi" : "unpackage/res/icons/144x144.png",
                                "ldpi" : "unpackage/res/icons/120x120.png",
                                "xhdpi" : "unpackage/res/icons/167x167.png",
                                "xxhdpi" : "unpackage/res/icons/180x180.png"
                            }
                        }
                    }
                }
            },
            "icons" : {
                "ios" : {
                    "iphone" : {
                        "app@2x" : "unpackage/res/icons/120x120.png",
                        "app@3x" : "unpackage/res/icons/180x180.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png",
                        "spotlight@3x" : "unpackage/res/icons/120x120.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "settings@3x" : "unpackage/res/icons/87x87.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "notification@3x" : "unpackage/res/icons/60x60.png"
                    },
                    "appstore" : "unpackage/res/icons/1024x1024.png",
                    "ipad" : {
                        "app" : "unpackage/res/icons/76x76.png",
                        "app@2x" : "unpackage/res/icons/152x152.png",
                        "notification" : "unpackage/res/icons/20x20.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "proapp@2x" : "unpackage/res/icons/167x167.png",
                        "settings" : "unpackage/res/icons/29x29.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "spotlight" : "unpackage/res/icons/40x40.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png"
                    }
                },
                "android" : {
                    "hdpi" : "unpackage/res/icons/72x72.png",
                    "xhdpi" : "unpackage/res/icons/96x96.png",
                    "xxhdpi" : "unpackage/res/icons/144x144.png",
                    "xxxhdpi" : "unpackage/res/icons/192x192.png"
                }
            },
            "splashscreen" : {
                "iosStyle" : "common"
            }
        }
    },
    /* 快应用特有相关 */
    "quickapp" : {},
    /* 小程序特有相关 */
    "mp-weixin" : {
        "appid" : "",
        "setting" : {
            "urlCheck" : false
        },
        "usingComponents" : true
    },
    "mp-alipay" : {
        "usingComponents" : true
    },
    "mp-baidu" : {
        "usingComponents" : true
    },
    "mp-toutiao" : {
        "usingComponents" : true
    },
    "uniStatistics" : {
        "enable" : false
    },
    "vueVersion" : "3",
    "fallbackLocale" : "zh-Hans",
    "locale" : "zh-Hans",
    "h5" : {
        "title" : "货运",
        "router" : {
            "mode" : "history"
        },
        "sdkConfigs" : {
            "maps" : {
                "amap" : {
                    "key" : "f771b56602350cd85ec928e0442c7512",
                    "securityJsCode" : "",
                    "serviceHost" : ""
                }
            }
        },
        "devServer" : {
            "port" : 8088, //前端端口号
            "proxy" : {
                "/transport" : {
                    "target" : "http://************:18099", //测试环境

                    // "target": "http://*********:8099",  //林老师local
                    "changeOrigin" : true, //是否跨域
                    "secure" : false, // 设置支持https协议的代理
                    "pathRewrite" : {
                        "^/transport" : ""
                    }
                }
            }
        }
    }
}
