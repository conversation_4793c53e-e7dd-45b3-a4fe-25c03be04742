# 微信支付配置说明

## 🚨 重要提醒
当前manifest.json中的微信支付配置使用了占位符，需要替换为真实的配置信息才能正常使用微信支付功能。

## 需要修改的文件
文件路径: `manifest.json`
修改位置: 第92-98行

## 当前配置问题
```json
"payment" : {
    "weixin" : {
        "__platform__" : [ "ios", "android" ],
        "appid" : "您的微信开放平台AppID",  // ❌ 占位符，需要替换
        "UniversalLinks" : "https://您的域名/universal-links/"  // ❌ 占位符，需要替换
    }
}
```

## 正确配置格式
```json
"payment" : {
    "weixin" : {
        "__platform__" : [ "ios", "android" ],
        "appid" : "wx1234567890abcdef",  // ✅ 真实的微信开放平台AppID
        "UniversalLinks" : "https://yourdomain.com/universal-links/"  // ✅ 真实的域名
    }
}
```

## 获取配置信息的步骤

### 1. 获取微信开放平台AppID
1. 登录 [微信开放平台](https://open.weixin.qq.com/)
2. 进入"管理中心" -> "移动应用"
3. 找到对应的APP应用
4. 复制AppID (格式: wx开头的字符串)

### 2. 配置UniversalLinks (iOS必需)
1. 确保拥有已备案的域名
2. 在域名根目录下创建 `.well-known/apple-app-site-association` 文件
3. 配置文件内容需要包含APP的Bundle ID
4. 在微信开放平台中配置相同的UniversalLinks地址

### 3. Android配置注意事项
- 确保APP签名与微信开放平台注册的签名一致
- 包名需要与微信开放平台注册的包名一致

## 配置验证
配置完成后，可以通过以下方式验证:
1. 编译APP到真机测试
2. 尝试调起微信支付
3. 检查是否能正常跳转到微信APP

## 常见问题
1. **支付调起失败**: 检查AppID是否正确
2. **iOS跳转异常**: 检查UniversalLinks配置
3. **Android签名错误**: 检查APP签名是否与平台注册一致

## 安全提醒
- 不要将真实的AppID提交到公开的代码仓库
- 建议使用环境变量或配置文件管理敏感信息
- 定期检查微信开放平台的配置是否有变更
