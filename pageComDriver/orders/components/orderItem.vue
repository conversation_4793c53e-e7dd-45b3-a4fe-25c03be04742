<template>
  <view class="order-list">
    <view class="order-item" v-for="(item, index) in orderList" :key="item.id || index" @click="clickItem(item)">
      <view class="status-box">
        <view class="status-left">
          <image src="@/static/images/yikoujia.png" class="yikoujia"></image>
          <!--
                    <text class="text">{{ item.type || '一口价' }}</text>
          -->
        </view>
        <view class="status-right">{{
          getOrderStatusText(item.orderStatus)
        }}</view>
      </view>
      <view class="driver-info">
        <view class="driver-left">
          <image :src="getImageSrc(item)" class="head" @error="handleImageError(item)"></image>
          <view class="text-info">
            <view class="name">{{ item.shippersName || '--' }}</view>
            <view class="car">{{ item.cargoName }} {{ item.vehicleLength }}{{ item.vehicleType }}</view>
          </view>
        </view>
        <view class="driver-right">
          <view class="icon" @click.stop="contactOwner(item)">
            <up-icon name="phone-fill" size="18"></up-icon>
          </view>
        </view>
      </view>
      <view class="trajectory-info">
        <view class="trajectory-left">
          <view class="start">
            <view class="start-address">{{
              item.loadingAddressName || '--'
            }}</view>
            <view class="start-time">{{ formatDate(item.loadingDate) }}
              {{ item.earliestLoadingTime }}</view>
          </view>
          <view class="start end">
            <view class="start-address">{{
              item.unloadingAddressName || '--'
            }}</view>
            <view class="start-time">{{ formatDate(item.loadingDate) }}
              {{ item.latestLoadingTime }}</view>
          </view>
        </view>
        <view class="trajectory-right" @click.stop="viewTrajectory(item)">
          <view class="iconfont icon-owner-guiji"></view>
          <text>查看轨迹</text>
        </view>
      </view>
      <view class="price-info">
        可退订金
        <text>¥{{ item.depositAmount }}</text>
        ({{ checkStatus(item.depositStatus) }}) 运费
        <text>¥{{ item.bidAmount }}</text>
        (待货主支付)
      </view>
      <view class="handle-box">
        <!--
                <view class="btn-span">删除订单</view>
        -->
        <!-- <view class="btn-span">催付运费</view> -->
        <!--
                <view class="btn-span btn-span-red">评价司机</view>
        -->
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { defineProps, defineEmits, ref } from 'vue'
import dayjs from 'dayjs'
import { makeCall } from '@/utils/phone'

// 图片加载失败状态管理
const imageErrorMap = ref<Record<string, boolean>>({})
// 定义接收的属性
const props = defineProps<{
  orderList: Array<{
    id?: number
    loadingAddressName?: string
    unloadingAddressName?: string
    cargoName?: string
    packagingMethod?: string
    vehicleLength?: string
    vehicleType?: string
    bidType?: string
    bidAmount?: string
    depositAmount?: number
    loadingDate?: string
    driverName?: string
    orderStatus?: string
    paymentMethod?: string
    earliestLoadingTime?: string
    latestLoadingTime?: string
    ownerPhone?: string
    driverPhone?: string
  }>
}>()

// 定义时间格式化函数
const formatDate = (timestamp: string | number | Date) => {
  if (!timestamp) return ''
  return dayjs(timestamp).format('YYYY-MM-DD')
}

const getOrderStatusText = (status: string | number) => {
  const statusMap: Record<string | number, string> = {
    0: '发货中',
    1: '已接单',
    2: '改价待确认',
    3: '改价已确认',
    4: '改价已拒绝',
    5: '到达装货地',
    6: '装货中',
    7: '运输中',
    8: '到达卸货地',
    9: '已完成待确认',
    10: '已完成',
    99: '已完成',
    98: '已取消',
  }
  return statusMap[status] || '未知状态'
}
const emit = defineEmits(['clickItem'])
const clickItem = (item: any) => {
  emit('clickItem', item)
}

// 图片加载错误处理
const handleImageError = (item: any) => {
  const key = item.id || item.shippersUrl || 'default'
  imageErrorMap.value[key] = true
}

// 获取图片源地址
const getImageSrc = (item: any) => {
  const key = item.id || item.shippersUrl || 'default'
  const hasError = imageErrorMap.value[key]

  // 如果图片加载失败或者没有图片URL，返回默认占位图
  if (hasError || !item.shippersUrl) {
    return '/static/images/head.png'
  }

  return item.shippersUrl
}

// 联系货主
const contactOwner = (item: any) => {
  const ownerPhone = item.ownerPhone || item.driverPhone
  if (!ownerPhone) {
    uni.showToast({
      title: '暂无联系电话',
      icon: 'none',
    })
    return
  }
  makeCall('13800138000')
}

// 查看轨迹 - 跳转到高德地图
const viewTrajectory = (item: any) => {
  console.log('查看轨迹', item)

  // 获取起点和终点信息
  const startLat = item.loadingLatitude
  const startLon = item.loadingLongitude
  const startName = item.loadingAddressName || '装货地'
  const endLat = item.unloadingLatitude
  const endLon = item.unloadingLongitude
  const endName = item.unloadingAddressName || '卸货地'

  // 检查是否有经纬度数据
  if (!startLat || !startLon || !endLat || !endLon) {
    uni.showToast({
      title: '暂无路线定位信息',
      icon: 'none',
    })
    return
  }

  // 构建高德地图URL Scheme
  const amapUrl = `amapuri://route/plan/?slat=${startLat}&slon=${startLon}&sname=${encodeURIComponent(
    startName
  )}&dlat=${endLat}&dlon=${endLon}&dname=${encodeURIComponent(
    endName
  )}&dev=0&t=0`

  console.log('高德地图导航URL:', amapUrl)

  // 平台兼容性处理
  // #ifdef APP-PLUS
  plus.runtime.openURL(amapUrl, (error) => {
    console.error('打开高德地图失败:', error)
    uni.showModal({
      title: '提示',
      content: '未检测到高德地图APP，是否前往下载？',
      success: (modalRes) => {
        if (modalRes.confirm) {
          const downloadUrl =
            uni.getSystemInfoSync().platform === 'ios'
              ? 'https://apps.apple.com/cn/app/id461703208'
              : 'https://www.amap.com/'
          plus.runtime.openURL(downloadUrl)
        }
      },
    })
  })
  // #endif

  // #ifdef H5
  window.location.href = amapUrl
  // #endif

  // #ifdef MP
  uni.setClipboardData({
    data: amapUrl,
    success: () => {
      uni.showToast({
        title: '导航链接已复制，请在高德地图中打开',
        icon: 'none',
        duration: 3000,
      })
    },
  })
  // #endif
}

// 订金状态
const checkStatus = (status: string | number) => {
  const statusMap: Record<string | number, string> = {
    0: '发货中',
    1: '待发起退订金',
    2: '司机已发起退订金',
    3: '货主同意退订金',
    4: '货主拒绝退订金',
    5: '订金冻结',
    6: '货主发起扣订金',
    7: '司机同意扣订金',
    8: '司机拒绝扣订金',
    9: '仲裁受理完毕',
    10: '订金已退还司机',
    11: '订金已支付货主',
  }
  return statusMap[status] || '--'
}
</script>

<style lang="scss" scoped>
.order-list {
  width: 100%;

  .order-item {
    background-color: #fff;
    padding: 0rpx 32rpx;
    box-sizing: border-box;
    border-radius: 26rpx;
    margin-top: 20rpx;

    &:first-child {
      margin-top: 0rpx;
    }

    .status-box {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20rpx 0rpx;
      border-bottom: 2rpx solid $uni-text-color-grey;

      .status-left {
        display: flex;
        align-items: center;

        .yikoujia {
          width: 50rpx;
          height: 50rpx;
        }

        .text {
          font-size: $uni-font-size-base-30;
          color: #000;
          margin-left: 16rpx;
        }
      }

      .status-right {
        font-size: $uni-font-size-sm;
        color: $uni-text-color-grey;
      }
    }

    .driver-info {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 10rpx 0rpx;
      border-bottom: 2rpx solid $uni-text-color-grey;

      .driver-left {
        display: flex;
        align-items: center;
        flex: 1;
        min-width: 0;
        max-width: calc(100% - 70rpx); // 为右侧电话图标留出空间

        .head {
          width: 60rpx;
          height: 60rpx;
          border-radius: 10rpx;
          flex-shrink: 0;
        }

        .text-info {
          display: flex;
          flex-direction: column;
          margin-left: 10rpx;
          flex: 1;
          min-width: 0;
        }

        .name {
          font-size: $uni-font-size-base;
          color: $uni-text-color;
          font-weight: 600;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          width: 100%;
          line-height: 1.4;
        }

        .car {
          font-size: $uni-font-size-base;
          color: $uni-text-color-grey;
          margin-top: 4rpx;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          width: 100%;
          line-height: 1.4;
        }
      }

      .driver-right {
        .icon {
          width: 50rpx;
          height: 50rpx;
          border: 2rpx solid #000;
          border-radius: 50%;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }
    }

    .trajectory-info {
      display: flex;
      justify-content: space-between;
      padding: 20rpx 0rpx;
      border-bottom: 2rpx solid $uni-text-color-grey;

      .trajectory-left {
        width: 540rpx;
        padding-left: 30rpx;

        .start {
          position: relative;

          &:before {
            content: '';
            width: 14rpx;
            height: 14rpx;
            background-color: #4997e9;
            border-radius: 50%;
            position: absolute;
            left: -30rpx;
            top: 8rpx;
          }

          .start-address {
            font-size: $uni-font-size-base;
            line-height: $uni-font-size-base;
            color: $uni-text-color;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .start-time {
            font-size: $uni-font-size-sm;
            line-height: $uni-font-size-sm;
            color: $uni-text-color-grey;
            margin-top: 10rpx;
          }
        }

        .end {
          margin-top: 20rpx;

          &:before {
            background-color: #e9534c;
          }
        }
      }

      .trajectory-right {
        width: 160rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        box-shadow: -14rpx 0rpx 10rpx -10rpx #ccc;

        .iconfont {
          font-size: 50rpx;
        }

        text {
          font-size: 22rpx;
          margin-top: 10rpx;
        }
      }
    }

    .price-info {
      font-size: $uni-font-size-sm;
      line-height: $uni-font-size-sm;
      color: $uni-text-color-grey;
      text-align: right;
      padding: 20rpx 0rpx;

      text {
        color: #e9534c;
        font-weight: 600;
      }
    }

    .handle-box {
      padding-bottom: 20rpx;
      display: flex;
      justify-content: flex-end;

      .btn-span {
        font-size: $uni-font-size-base;
        line-height: $uni-font-size-base;
        padding: 12rpx 12rpx;
        box-sizing: border-box;
        border: 2rpx solid #333;
        border-radius: 8rpx;
        margin-left: 10rpx;
      }

      .btn-span-red {
        background-color: #e7473f;
        color: #fff;
        border: none;
      }
    }
  }
}
</style>
