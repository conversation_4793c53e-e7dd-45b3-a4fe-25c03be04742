<template>
  <view class="pay-container">
    <!-- 头部导航 -->
    <view class="header">
      <view class="header-content">
        <view class="back-btn" @click="goBack">
          <view class="iconfont icon-owner-a-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"></view>
        </view>
        <text class="header-title">订单支付</text>
        <view class="placeholder"></view>
      </view>
    </view>

    <!-- 内容区域，添加顶部间距 -->
    <view class="content-area">
      <!-- 订单信息卡片 -->
      <view class="order-card">
        <view class="order-header">
          <text class="order-title">订单信息</text>
          <text class="order-status">待支付</text>
        </view>
        <view class="order-info">
          <view class="info-row">
            <text class="label">订单号：</text>
            <text class="value">{{ orderId }}</text>
          </view>
          <view class="info-row">
            <text class="label">货物信息：</text>
            <text class="value">{{ orderDetail.cargoName || '危险货物运输' }}</text>
          </view>
          <view class="info-row">
            <text class="label">装货时间：</text>
            <text class="value">{{ formatOrderDate() }}</text>
          </view>
          <view class="info-row">
            <text class="label">总运费：</text>
            <text class="value">¥{{ formatAmount(orderDetail.bidAmount || 0) }}</text>
          </view>
        </view>
      </view>

      <!-- 金额信息 -->
      <view class="amount-card">
        <view class="amount-header">
          <text class="amount-title">支付金额</text>
        </view>
        <view class="amount-content">
          <text class="currency">¥</text>
          <text class="amount">{{ formatAmount(money) }}</text>
        </view>
        <view class="amount-desc">
          <text>订金金额（{{ orderDetail.depositRefundType === 'refund' ? '退还' : '不退还' }}）</text>
        </view>
      </view>

      <!-- 支付方式选择 -->
      <view class="payment-methods">
        <view class="methods-header">
          <text class="methods-title">选择支付方式</text>
        </view>
        <view class="methods-list">
          <!-- 微信支付 -->
          <view class="method-item" :class="{ active: selectedMethod === 'wechat' }" @click="selectPaymentMethod('wechat')">
            <view class="method-left">
              <view class="method-icon wechat">
                <image src="@/static/images/wx_pay.png" class="icon-image" mode="aspectFit"></image>
              </view>
              <view class="method-info">
                <text class="method-name">微信支付</text>
                <text class="method-desc">推荐使用微信支付</text>
              </view>
            </view>
            <view class="method-radio" :class="{ checked: selectedMethod === 'wechat' }">
              <view class="radio-inner" v-if="selectedMethod === 'wechat'"></view>
            </view>
          </view>

          <!-- 支付宝支付 -->
          <view class="method-item" :class="{ active: selectedMethod === 'alipay', disabled: !alipayEnabled }" @click="selectPaymentMethod('alipay')">
            <view class="method-left">
              <view class="method-icon alipay">
                <image src="@/static/images/ali_pay.png" class="icon-image" mode="aspectFit"></image>
              </view>
              <view class="method-info">
                <text class="method-name">支付宝支付</text>
                <text class="method-desc">{{ alipayEnabled ? '安全便捷的支付方式' : '即将开放' }}</text>
              </view>
            </view>
            <view class="method-radio" :class="{ checked: selectedMethod === 'alipay', disabled: !alipayEnabled }">
              <view class="radio-inner" v-if="selectedMethod === 'alipay'"></view>
            </view>
          </view>
        </view>
      </view>
    </view> <!-- 关闭 content-area -->

    <!-- 底部支付按钮 -->
    <view class="bottom-container">
      <view class="pay-summary">
        <text class="pay-amount">合计：¥{{ formatAmount(money) }}</text>
      </view>
      <view class="pay-button" :class="{ disabled: !canPay }" @click="handlePay">
        <view v-if="paymentLoading" class="loading">
          <text class="loading-text">支付中...</text>
        </view>
        <text v-else class="pay-text">
          {{ getPayButtonText() }}
        </text>
      </view>
    </view>

    <!-- 支付确认弹窗 -->
    <cu-confirm-dialog ref="confirmDialog" title="确认支付" :content="`确认支付 ¥${formatAmount(money)} 吗？`" important-tips="支付后将无法撤销，请确认订单信息无误" cancel-text="取消" confirm-text="确认支付" @cancel="cancelPay" @confirm="confirmPay" />
  </view>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, onMounted, onUpdated } from 'vue'
import { onLoad, onShow } from '@dcloudio/uni-app'
import { orderPay, alipayPay, getOrderInfoById } from "../../api/transport"
import { getStoragetItem } from '@/utils/storage'
import { formatDate, formatDateTime } from '@/utils/common'

// 获取系统信息
const systemInfo: any = uni.getSystemInfoSync()

// 底部固定区域高度
const bottomHeight = ref('200rpx') // 默认值

// 计算底部固定区域高度
const calculateBottomHeight = () => {
  nextTick(() => {
    const query = uni.createSelectorQuery()
    query
      .select('.bottom-container')
      .boundingClientRect((rect: any) => {
        if (rect && rect.height) {
          // 获取系统信息用于准确的px到rpx转换
          const systemInfo = uni.getSystemInfoSync()

          // 更准确的px到rpx转换：rpx = px * 750 / 屏幕宽度
          const screenWidth = systemInfo.screenWidth || 375
          const heightInRpx = Math.ceil((rect.height * 750) / screenWidth)

          // 添加一些额外的安全边距，确保内容不被遮挡
          const safeMargin = 20 // 20rpx的安全边距
          bottomHeight.value = heightInRpx + safeMargin + 'rpx'

          console.log('底部支付区域高度计算:', {
            原始高度: rect.height + 'px',
            转换后高度: heightInRpx + 'rpx',
            最终高度: bottomHeight.value,
            屏幕宽度: screenWidth
          })
        } else {
          console.warn('无法获取底部支付区域的尺寸信息')
          // 如果无法获取尺寸，使用一个合理的默认值
          bottomHeight.value = '200rpx'
        }
      })
      .exec()
  })
}

// 防抖函数
const debounce = (func: Function, wait: number) => {
  let timeout: any
  return function executedFunction(...args: any[]) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

// 防抖的计算函数
const debouncedCalculateHeight = debounce(calculateBottomHeight, 300)

// 响应式数据
const money = ref<number>(0.00)
const orderId = ref<string>('')
const orderDetail = ref<any>({})
const selectedMethod = ref<string>('wechat') // 默认选择微信支付

const paymentLoading = ref<boolean>(false)
const alipayEnabled = ref<boolean>(false) // 支付宝支付开关，默认禁用，只支持微信支付

// 弹窗引用
const confirmDialog = ref(null)

// 页面加载时获取参数
onLoad((options: any) => {
  if (options.orderId) {
    orderId.value = options.orderId
    fetchOrderDetail(options.orderId)
  } else {
    uni.showToast({
      title: '订单ID参数缺失',
      icon: 'error',
      duration: 2000
    })
    setTimeout(() => {
      uni.navigateBack()
    }, 2000)
  }
})

// 获取订单详情的函数
const fetchOrderDetail = async (id: string) => {
  try {
    uni.showLoading({
      title: '加载中...'
    })

    // 调用接口获取订单详情
    const response: any = await getOrderInfoById(id)
    uni.hideLoading()

    if (response.code === 700) {
      orderDetail.value = response.result
      // 设置支付金额为订金金额
      money.value = parseFloat(orderDetail.value.depositAmount) || 0.00
      console.log('订单详情获取成功:', orderDetail.value)
      console.log('订金金额:', money.value)

      // 数据加载完成后重新计算底部高度
      nextTick(() => {
        setTimeout(() => {
          calculateBottomHeight()
        }, 300)
      })
    } else {
      uni.showToast({
        title: response.message || '获取订单详情失败',
        icon: 'error',
        duration: 2000
      })
    }
  } catch (error) {
    uni.hideLoading()
    console.error('获取订单详情失败:', error)
    uni.showToast({
      title: '获取订单详情失败，请重试',
      icon: 'error',
      duration: 2000
    })
  }
}

// 计算属性
const canPay = computed(() => {
  return selectedMethod.value && !paymentLoading.value && money.value > 0
})

// 格式化金额显示
const formatAmount = (amount: number): string => {
  return amount.toFixed(2)
}

// 格式化订单日期
const formatOrderDate = (): string => {
  if (!orderDetail.value.loadingDate) return ''
  const formattedDate = formatDate(orderDetail.value.loadingDate)
  const earliestTime = orderDetail.value.earliestLoadingTime || ''
  const latestTime = orderDetail.value.latestLoadingTime || ''
  return `${formattedDate} ${earliestTime}-${latestTime}`
}

// 获取支付按钮文本
const getPayButtonText = (): string => {
  if (!selectedMethod.value) {
    return '请选择支付方式'
  }
  if (selectedMethod.value === 'wechat') {
    return '微信支付'
  }
  if (selectedMethod.value === 'alipay') {
    return '支付宝支付'
  }
  return '立即支付'
}

// 选择支付方式
const selectPaymentMethod = (method: string) => {
  if (method === 'alipay' && !alipayEnabled.value) {
    uni.showToast({
      title: '支付宝支付即将开放',
      icon: 'none',
      duration: 2000
    })
    return
  }
  selectedMethod.value = method
}



// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

// 处理支付点击
const handlePay = () => {
  if (!canPay.value) {
    return
  }

  // 显示确认弹窗
  confirmDialog.value?.open()
}

// 取消支付
const cancelPay = () => {
  // cu-confirm-dialog 组件会自动关闭，无需手动关闭
  console.log('用户取消支付')
}

// 确认支付
const confirmPay = () => {
  // cu-confirm-dialog 组件会自动关闭，无需手动关闭
  if (selectedMethod.value === 'wechat') {
    processWechatPay()
  } else if (selectedMethod.value === 'alipay') {
    processAlipayPay()
  }
}

// 处理微信支付
const processWechatPay = async () => {
  try {
    paymentLoading.value = true

    // 第一步：调用后端统一下单接口（原生APP不需要openid）
    const userInfo = getStoragetItem('userInfo')
    const response = await orderPay({
      money: money.value,
      orderNum: orderId.value,
      userId: userInfo?.id || '',
      paymentType: 'APP' // 标识为原生APP支付
    })

    if (response.code === 700 && response.result) {
      // 第二步：使用后端返回的支付参数调起微信支付
      const paymentParams = response.result
      
      await uni.requestPayment({
        provider: 'wxpay',
        appid: paymentParams.appid,
        partnerid: paymentParams.partnerid,
        prepayid: paymentParams.prepayid,
        package: paymentParams.package || 'Sign=WXPay',
        noncestr: paymentParams.noncestr,
        timestamp: paymentParams.timestamp,
        sign: paymentParams.sign,
        
        success: (result) => {
          console.log('微信支付成功:', result)
          uni.showToast({
            title: '支付成功',
            icon: 'success',
            duration: 2000
          })
          
          // 支付成功后的处理
          setTimeout(() => {
            uni.reLaunch({
              url: "/pages/index/index"
            })
          }, 2000)
        },
        
        fail: (error) => {
          console.error('微信支付失败:', error)
          
          // 根据错误类型显示不同提示
          if (error.errMsg && error.errMsg.includes('cancel')) {
            uni.showToast({
              title: '支付已取消',
              icon: 'none',
              duration: 2000
            })
          } else {
            uni.showToast({
              title: '支付失败，请重试',
              icon: 'error',
              duration: 2000
            })
          }
        }
      })
    } else {
      // 统一下单失败
      uni.showToast({
        title: response.message || '支付失败',
        icon: 'error',
        duration: 2000
      })
    }
  } catch (error) {
    console.error('微信支付失败:', error)
    uni.showToast({
      title: '支付失败，请重试',
      icon: 'error',
      duration: 2000
    })
  } finally {
    paymentLoading.value = false
  }
}

// 处理支付宝支付（预留接口）
const processAlipayPay = async () => {
  try {
    paymentLoading.value = true

    // 支付宝支付API调用（当后端接口准备好时启用）
    const response = await alipayPay({
      amount: money.value,
      orderNum: orderId.value,
      userId: getStoragetItem('userId') || 'default_user'
    })

    if (response.code === 700) {
      // 支付成功
      uni.showToast({
        title: '支付成功',
        icon: 'success',
        duration: 2000
      })

      setTimeout(() => {
        uni.reLaunch({
          url: "/pages/index/index"
        })
      }, 2000)
    } else {
      // 支付失败
      uni.showToast({
        title: response.message || '支付失败',
        icon: 'error',
        duration: 2000
      })
    }

  } catch (error) {
    console.error('支付宝支付失败:', error)

    // 如果是接口未实现的错误，显示开发中提示
    if (error.message && error.message.includes('404')) {
      uni.showToast({
        title: '支付宝支付功能开发中',
        icon: 'none',
        duration: 2000
      })
    } else {
      uni.showToast({
        title: '支付失败，请重试',
        icon: 'error',
        duration: 2000
      })
    }
  } finally {
    paymentLoading.value = false
  }
}

// 组件挂载后计算高度
onMounted(() => {
  // 延迟计算，确保DOM完全渲染
  setTimeout(() => {
    calculateBottomHeight()
  }, 100)

  // 监听窗口大小变化
  uni.onWindowResize(() => {
    debouncedCalculateHeight()
  })
})

// 组件更新后重新计算高度
onUpdated(() => {
  debouncedCalculateHeight()
})

// 页面显示时重新计算高度（处理从其他页面返回的情况）
onShow(() => {
  console.log('页面显示，重新计算底部高度')
  setTimeout(() => {
    calculateBottomHeight()
  }, 200)
})
</script>

<style lang="scss" scoped>
.pay-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

// 头部导航
.header {
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  /* header整体高度 = 状态栏高度 + 导航栏高度 */
  height: calc(var(--status-bar-height, env(safe-area-inset-top)) + 88rpx);
  padding-top: var(--status-bar-height, env(safe-area-inset-top));
  box-sizing: border-box;

  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 88rpx;
    padding: 0 32rpx;

    .back-btn {
      width: 48rpx;
      height: 48rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      .iconfont {
        font-size: 36rpx;
        color: #333;
      }
    }

    .header-title {
      font-size: 36rpx;
      font-weight: 600;
      color: #333;
    }

    .placeholder {
      width: 48rpx;
    }
  }
}

// 内容区域
.content-area {
  /* 顶部间距 = header整体高度 = 状态栏高度 + 导航栏高度 */
  padding-top: calc(var(--status-bar-height, env(safe-area-inset-top)) + 88rpx);
  /* 为底部固定区域留出空间 */
  padding-bottom: v-bind(bottomHeight);
}

// 订单信息卡片
.order-card {
  background-color: #fff;
  margin: 24rpx 32rpx;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);

  .order-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24rpx;

    .order-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }

    .order-status {
      font-size: 24rpx;
      color: #ff6b35;
      background-color: #fff2f0;
      padding: 8rpx 16rpx;
      border-radius: 8rpx;
    }
  }

  .order-info {
    .info-row {
      display: flex;
      align-items: center;
      margin-bottom: 16rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        font-size: 28rpx;
        color: #666;
        width: 160rpx;
      }

      .value {
        font-size: 28rpx;
        color: #333;
        flex: 1;
      }
    }
  }
}

// 金额信息
.amount-card {
  background-color: #fff;
  margin: 0 32rpx 24rpx;
  border-radius: 16rpx;
  padding: 40rpx 32rpx;
  text-align: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);

  .amount-header {
    margin-bottom: 24rpx;

    .amount-title {
      font-size: 28rpx;
      color: #666;
    }
  }

  .amount-content {
    display: flex;
    align-items: baseline;
    justify-content: center;
    margin-bottom: 16rpx;

    .currency {
      font-size: 36rpx;
      color: #ff6b35;
      margin-right: 8rpx;
    }

    .amount {
      font-size: 72rpx;
      font-weight: 600;
      color: #ff6b35;
    }
  }

  .amount-desc {
    .text {
      font-size: 24rpx;
      color: #999;
    }
  }
}

// 支付方式选择
.payment-methods {
  background-color: #fff;
  margin: 0 32rpx 24rpx;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);

  .methods-header {
    margin-bottom: 24rpx;

    .methods-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }
  }

  .methods-list {
    .method-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 24rpx 0;
      border-bottom: 1rpx solid #f0f0f0;
      transition: all 0.3s ease;

      &:last-child {
        border-bottom: none;
      }

      &.active {
        background-color: #f8f9ff;
        border-radius: 12rpx;
        padding: 24rpx 16rpx;
        margin: 0 -16rpx;
      }

      &.disabled {
        opacity: 0.5;

        .method-left {
          opacity: 0.6;
        }
      }

      .method-left {
        display: flex;
        align-items: center;
        flex: 1;

        .method-icon {
          width: 72rpx;
          height: 72rpx;
          border-radius: 12rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 24rpx;

          &.wechat {
            background-color: transparent;

            .icon-image {
              width: 48rpx;
              height: 48rpx;
            }
          }

          &.alipay {
            background-color: transparent;

            .icon-image {
              width: 48rpx;
              height: 48rpx;
            }
          }
        }

        .method-info {
          flex: 1;

          .method-name {
            display: block;
            font-size: 32rpx;
            font-weight: 500;
            color: #333;
            margin-bottom: 8rpx;
          }

          .method-desc {
            font-size: 24rpx;
            color: #666;
          }
        }
      }

      .method-radio {
        width: 40rpx;
        height: 40rpx;
        border: 2rpx solid #ddd;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;

        &.checked {
          border-color: #1677ff;
          background-color: #1677ff;
        }

        &.disabled {
          border-color: #ddd;
          background-color: #f5f5f5;
        }

        .radio-inner {
          width: 20rpx;
          height: 20rpx;
          background-color: #fff;
          border-radius: 50%;
        }
      }
    }
  }
}



// 底部支付区域
.bottom-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  border-top: 1rpx solid #eee;
  padding: 24rpx 32rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.1);
  /* 添加阴影增强层次感 */

  .pay-summary {
    margin-bottom: 24rpx;
    text-align: right;

    .pay-amount {
      font-size: 32rpx;
      font-weight: 600;
      color: #ff6b35;
    }
  }

  .pay-button {
    width: 100%;
    height: 88rpx;
    background: linear-gradient(135deg, #1677ff 0%, #4096ff 100%);
    border-radius: 44rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 0 8rpx 24rpx rgba(22, 119, 255, 0.3);

    &.disabled {
      background: #f5f5f5;
      color: #ccc;
      box-shadow: none;
    }

    .loading {
      display: flex;
      align-items: center;

      .loading-text {
        font-size: 32rpx;
        color: #fff;
        margin-left: 16rpx;
      }
    }

    .pay-text {
      font-size: 32rpx;
      font-weight: 600;
      color: #fff;
    }
  }
}
</style>
