import request from '../../utils/request'

// 获取所有车型
export function getModel() {
  return request({
    url: `/carType/allCarTypeList`,
    method: 'POST',
    loading: true,
  })
}

// 获取所有车长
export function getCarLengthList() {
  return request({
    url: `/carLength/allCarLengthList`,
    method: 'POST',
    loading: true,
  })
}

// 获取所有车型go
export function getModelGo() {
  return request({
    url: `/carType/allCarTypeListByGo`,
    method: 'POST',
    loading: true,
  })
}

// 获取所有车长go
export function getCarLengthListGo() {
  return request({
    url: `/carLength/allCarLengthListByGo`,
    method: 'POST',
    loading: true,
  })
}

// 修改订单信息（新增、修改）
export function editOrderInfo(data: any) {
  return request({
    url: `/orderInfo/editOrderInfo`,
    method: 'POST',
    data,
    loading: true,
  })
}

// 订单信息分页查询列表
export function orderInfoPage(data: any) {
  return request({
    url: `/orderInfo/orderInfoPage`,
    method: 'POST',
    data,
    loading: true,
  })
}

// 订单信息分页查询列表
export function allOrderInfoList(data: any) {
  return request({
    url: `/orderInfo/allOrderInfoList`,
    method: 'POST',
    data,
    loading: true,
  })
}

// 订单信息通过编号查询
export function getOrderInfoById(data: any) {
  return request({
    url: `/orderInfo/getOrderInfoById/${data}`,
    method: 'GET',
    loading: true,
  })
}

// 司机抢单
export function driverGrabOrders(data: any) {
  return request({
    url: `/orderInfo/driverGrabOrders`,
    method: 'POST',
    data,
    loading: true,
  })
}

// 取消抢单
export function driverCancelOrder(data: any) {
  return request({
    url: `/orderInfo/driverCancelOrder`,
    method: 'POST',
    data,
    loading: true,
  })
}

// 微信支付押金
export function WXPay(data: any) {
  return request({
    url: `/orderInfo/WXPay`,
    method: 'POST',
    data,
    loading: true,
  })
}

// 微信退押金
export function refund(data: any) {
  return request({
    url: `/orderInfo/refund`,
    method: 'POST',
    data,
    loading: true,
  })
}

// 当前登录司机的订阅
export function allDriverSubscribeList() {
  return request({
    url: `/driverSubscribe/allDriverSubscribeList`,
    method: 'POST',
    loading: true,
  })
}

// 新增或修改司机订阅
export function editDriverSubscribe(data: any) {
  return request({
    url: `/driverSubscribe/editDriverSubscribe`,
    method: 'POST',
    data,
    loading: true,
  })
}

// 删除司机订阅
export function deleteDriverSubscribe(data: any) {
  return request({
    url: `/driverSubscribe/deleteDriverSubscribe`,
    method: 'POST',
    data,
    loading: true,
  })
}

// 配货大厅订阅线路查询
export function getDistributionDriver(data: any) {
  return request({
    url: `/orderInfo/getDistributionDriver`,
    method: 'POST',
    data,
    loading: true,
  })
}

// 配货大厅订阅新货查询
export function getNewDistributionDriver() {
  return request({
    url: `/orderInfo/getNewDistributionDriver`,
    method: 'POST',
    loading: true,
  })
}

// 订单支付（微信支付）
export function orderPay(data: any) {
  return request({
    url: `/orderPay/pay`,
    method: 'GET',
    data,
    loading: true,
  })
}

// 支付宝支付（预留接口）
export function alipayPay(data: any) {
  return request({
    url: `/orderPay/alipay`,
    method: 'POST',
    data,
    loading: true,
  })
}

// 定金处理
export function editDeposit(data: any) {
  return request({
    url: `/orderInfo/editDeposit`,
    method: 'POST',
    data,
    loading: true,
  })
}

// 获取登录用户
export function getLoginUser() {
  return request({
    url: `/user/getLoginUserInfo`,
    method: 'POST',
    loading: true,
  })
}

// 保存用户信息
export function saveLoginUser(data: any) {
  return request({
    url: `/user/saveLoginUserInfo`,
    method: 'POST',
    data,
    loading: true,
  })
}

// 资质审核
export function qualificationsAudit(data: any) {
  return request({
    url: `/user/qualificationsAudit`,
    method: 'POST',
    data,
    loading: true,
  })
}

// 登录后更新用户类型，用于登录后选择个人货主不需要认证时候
export function saveLoginUserInfo(data: any) {
  return request({
    url: `/user/saveLoginUserInfo`,
    method: 'POST',
    data,
    loading: true,
  })
}

// 货主删除订单
export function deleteOrderInfo(id: any) {
  return request({
    url: `/orderInfo/deleteOrderInfo?primaryKey=${id}`,
    method: 'POST',
    loading: true,
  })
}

// 订单状态处理
export function editOrderStatus(data: any) {
  return request({
    url: `/orderInfo/editDeposit`,
    method: 'POST',
    data,
    loading: true,
  })
}
